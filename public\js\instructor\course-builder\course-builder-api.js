/**
 * Course Builder API Module
 * Handles all API communication for saving data
 */

// Save functions
function saveCourse() {
    const form = document.getElementById('course-details-form');
    if (!form) return;

    const formData = new FormData(form);

    fetch(`/instructor/courses/${courseId}`, {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Course details saved successfully');
            updateCourseOverview(data.data);
        } else {
            showError(data.message || 'Failed to save course details');
        }
    })
    .catch(error => {
        console.error('Error saving course:', error);
        showError('Network error while saving course details');
    });
}

function autoSaveCourse() {
    // Auto-save functionality disabled - use manual save instead
    console.log('Auto-save disabled for course');
}

function updateCourseOverview(courseData) {
    // Update course title in overview
    const titleElements = document.querySelectorAll('.course-title');
    titleElements.forEach(element => {
        element.textContent = courseData.title;
    });

    // Update subtitle if exists
    if (courseData.subtitle) {
        const subtitleElements = document.querySelectorAll('.course-subtitle');
        subtitleElements.forEach(element => {
            element.textContent = courseData.subtitle;
        });
    }

    // Update price display
    const priceElements = document.querySelectorAll('.course-price');
    priceElements.forEach(element => {
        if (courseData.price > 0) {
            element.innerHTML = `<span class="text-2xl font-bold text-green-400">$${parseFloat(courseData.price).toFixed(2)}</span>`;
        } else {
            element.innerHTML = `<span class="text-2xl font-bold text-blue-400">FREE</span>`;
        }
    });
}

function saveChapter(chapterId) {
    const form = document.getElementById(`chapter-form-${chapterId}`);
    if (!form) return;

    const formData = new FormData(form);
    
    // Convert learning objectives from textarea to array
    const learningObjectivesText = formData.get('learning_objectives');
    if (learningObjectivesText) {
        const objectives = learningObjectivesText.split('\n').filter(obj => obj.trim() !== '');
        formData.delete('learning_objectives');
        formData.append('learning_objectives', JSON.stringify(objectives));
    }

    fetch(`/instructor/courses/${courseId}/chapters/${chapterId}`, {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Chapter saved successfully');
            // Update chapter title in sidebar
            const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"] .text-white`);
            if (chapterElement && data.data.title) {
                chapterElement.textContent = data.data.title;
            }
        } else {
            showError(data.message || 'Failed to save chapter');
        }
    })
    .catch(error => {
        console.error('Error saving chapter:', error);
        showError('Network error while saving chapter');
    });
}

function autoSaveChapter(chapterId) {
    // Auto-save functionality disabled - use manual save instead
    console.log('Auto-save disabled for chapter:', chapterId);
}

function saveLecture(lectureId) {
    const form = document.getElementById(`lecture-form-${lectureId}`);
    if (!form) return;

    const formData = new FormData(form);

    fetch(`/instructor/courses/${courseId}/lectures/${lectureId}`, {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Lecture saved successfully');
            // Update lecture title in sidebar
            const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"] .text-gray-300`);
            if (lectureElement && data.data.title) {
                lectureElement.textContent = data.data.title;
            }
            // Update lecture icon based on type
            const iconElement = document.querySelector(`[data-lecture-id="${lectureId}"] i`);
            if (iconElement && data.data.type) {
                iconElement.className = getLectureIcon(data.data.type);
            }
        } else {
            showError(data.message || 'Failed to save lecture');
        }
    })
    .catch(error => {
        console.error('Error saving lecture:', error);
        showError('Network error while saving lecture');
    });
}

function autoSaveLecture(lectureId) {
    // Auto-save functionality disabled - use manual save instead
    console.log('Auto-save disabled for lecture:', lectureId);
}

function getLectureIcon(type) {
    const icons = {
        'video': 'fas fa-play-circle text-blue-500',
        'text': 'fas fa-file-text text-green-500',
        'quiz': 'fas fa-question-circle text-purple-500',
        'assignment': 'fas fa-tasks text-orange-500',
        'resource': 'fas fa-download text-gray-500'
    };
    return icons[type] || 'fas fa-file text-gray-500';
}

function togglePublishStatus() {
    const button = document.getElementById('publish-toggle-btn');
    if (!button) return;

    const currentStatus = button.getAttribute('data-current-status');
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';

    fetch(`/instructor/courses/${courseId}/toggle-publish`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button appearance and text
            button.setAttribute('data-current-status', newStatus);
            
            if (newStatus === 'published') {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-green-600 hover:bg-green-700 text-white';
                button.innerHTML = '<i class="fas fa-eye mr-1 md:mr-2"></i><span class="hidden sm:inline">Published</span><span class="sm:hidden">Live</span>';
                showSuccess('Course published successfully');
            } else {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-red-600 hover:bg-red-700 text-white';
                button.innerHTML = '<i class="fas fa-eye-slash mr-1 md:mr-2"></i><span class="hidden sm:inline">Publish Course</span><span class="sm:hidden">Publish</span>';
                showSuccess('Course unpublished successfully');
            }
        } else {
            showError(data.message || 'Failed to update course status');
        }
    })
    .catch(error => {
        console.error('Error toggling publish status:', error);
        showError('Network error while updating course status');
    });
}
