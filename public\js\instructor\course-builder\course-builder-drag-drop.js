/**
 * Course Builder Drag and Drop Module
 * Handles drag and drop functionality for chapters and lectures
 */

function initializeDragAndDrop() {
    // Make chapters sortable
    const curriculumTree = document.getElementById('curriculum-tree');
    if (curriculumTree) {
        new Sortable(curriculumTree, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onStart: function() {
                isDragging = true;
            },
            onEnd: function(evt) {
                isDragging = false;
                if (evt.oldIndex !== evt.newIndex) {
                    reorderChapters();
                }
            }
        });
    }

    // Make lectures sortable within each chapter
    document.querySelectorAll('.chapter-lectures').forEach(lecturesContainer => {
        new Sortable(lecturesContainer, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            filter: '.add-lecture-btn',
            onStart: function() {
                isDragging = true;
            },
            onEnd: function(evt) {
                isDragging = false;
                if (evt.oldIndex !== evt.newIndex) {
                    const chapterId = lecturesContainer.id.replace('chapter-lectures-', '');
                    reorderLectures(chapterId);
                }
            }
        });
    });
}

function reorderChapters() {
    const chapterElements = document.querySelectorAll('.chapter-item');
    const chapterIds = Array.from(chapterElements).map(element => 
        element.getAttribute('data-chapter-id')
    );

    // Send reorder request to backend
    fetch(`/instructor/courses/${courseId}/chapters/reorder`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ chapter_ids: chapterIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Chapters reordered successfully');
        } else {
            console.error('Failed to reorder chapters:', data.message);
            showError('Failed to reorder chapters');
        }
    })
    .catch(error => {
        console.error('Error reordering chapters:', error);
        showError('Network error while reordering chapters');
    });
}

function reorderLectures(chapterId) {
    const lectureElements = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`);
    const lectureIds = Array.from(lectureElements).map(element => 
        element.getAttribute('data-lecture-id')
    );

    // Send reorder request to backend
    fetch(`/instructor/courses/${courseId}/chapters/${chapterId}/lectures/reorder`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ lecture_ids: lectureIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Lectures reordered successfully');
        } else {
            console.error('Failed to reorder lectures:', data.message);
            showError('Failed to reorder lectures');
        }
    })
    .catch(error => {
        console.error('Error reordering lectures:', error);
        showError('Network error while reordering lectures');
    });
}
