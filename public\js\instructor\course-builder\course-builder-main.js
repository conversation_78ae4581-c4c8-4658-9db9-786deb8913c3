/**
 * Course Builder Main Module
 * Handles initialization, mobile navigation, and core functionality
 */

// Global variables
let courseId = '';
let currentSelection = { type: null, id: null };
let autoSaveTimeouts = new Map();
let isDragging = false;

// Initialize the course builder when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get course ID from meta tag or data attribute
    const courseElement = document.querySelector('[data-course-id]');
    if (courseElement) {
        courseId = courseElement.getAttribute('data-course-id');
    }
    
    // Initialize the course builder
    initializeCourseBuilder();
});

function initializeCourseBuilder() {
    // Initialize mobile navigation
    initializeMobileNavigation();

    // Initialize drag and drop
    initializeDragAndDrop();

    // Initialize auto-save
    initializeAutoSave();

    // Initialize event listeners
    initializeEventListeners();

    // Show course details by default if no chapters
    if (document.querySelectorAll('.chapter-item').length === 0) {
        selectItem('course', courseId);
    }
}

function initializeMobileNavigation() {
    const mobileToggle = document.getElementById('mobile-sidebar-toggle');
    const mobileClose = document.getElementById('mobile-sidebar-close');
    const sidebar = document.getElementById('curriculum-sidebar');
    const overlay = document.getElementById('mobile-sidebar-overlay');

    // Mobile sidebar toggle
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.remove('-translate-x-full');
            overlay.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        });
    }

    // Mobile sidebar close
    if (mobileClose) {
        mobileClose.addEventListener('click', closeMobileSidebar);
    }

    // Overlay click to close
    if (overlay) {
        overlay.addEventListener('click', closeMobileSidebar);
    }

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
            closeMobileSidebar();
        }
    });

    // Auto-close sidebar on desktop resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) { // lg breakpoint
            closeMobileSidebar();
        }
    });

    function closeMobileSidebar() {
        sidebar.classList.add('-translate-x-full');
        overlay.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
}

function initializeEventListeners() {
    // Add chapter buttons (desktop and mobile)
    document.getElementById('add-chapter-btn')?.addEventListener('click', addChapter);
    document.getElementById('add-chapter-btn-mobile')?.addEventListener('click', function() {
        addChapter();
        // Close mobile/tablet sidebar after adding chapter
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');
        if (window.innerWidth < 1024) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });
    document.getElementById('add-first-chapter-btn')?.addEventListener('click', addChapter);

    // Publish toggle button
    document.getElementById('publish-toggle-btn')?.addEventListener('click', togglePublishStatus);

    // Prevent form submission
    document.getElementById('course-details-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
    });

    // Touch-friendly interactions for mobile
    if ('ontouchstart' in window) {
        // Add touch feedback for interactive elements
        document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.backgroundColor = 'rgba(127, 29, 29, 0.2)';
            });
            item.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 150);
            });
        });
    }
}

function selectItem(type, id) {
    if (isDragging) return; // Don't select during drag operations

    currentSelection = { type, id };

    // Update sidebar selection
    updateSidebarSelection(type, id);

    // Load content in main editor
    loadEditor(type, id);

    // Close mobile/tablet sidebar when selecting an item
    if (window.innerWidth < 1024) {
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');
        if (sidebar && overlay) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    }
}

function updateSidebarSelection(type, id) {
    // Remove existing selections
    document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
        item.classList.remove('bg-red-900', 'border-red-600');
    });

    // Add selection to current item
    if (type === 'chapter') {
        const chapterItem = document.querySelector(`[data-chapter-id="${id}"]`);
        if (chapterItem) {
            chapterItem.classList.add('bg-red-900', 'border-red-600');
        }
    } else if (type === 'lecture') {
        const lectureItem = document.querySelector(`[data-lecture-id="${id}"]`);
        if (lectureItem) {
            lectureItem.classList.add('bg-red-900', 'border-red-600');
        }
    }
}

// Helper functions for DOM manipulation
function toggleChapter(chapterId) {
    const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
    const toggleIcon = document.querySelector(`[data-chapter-id="${chapterId}"] .chapter-toggle`);
    
    if (lecturesContainer && toggleIcon) {
        if (lecturesContainer.style.display === 'none') {
            lecturesContainer.style.display = 'block';
            toggleIcon.classList.remove('fa-chevron-right');
            toggleIcon.classList.add('fa-chevron-down');
        } else {
            lecturesContainer.style.display = 'none';
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-right');
        }
    }
}

// Expose functions to global scope for inline event handlers
window.selectItem = selectItem;
window.toggleChapter = toggleChapter;
window.addChapter = addChapter;
window.addLecture = addLecture;
window.deleteChapter = deleteChapter;
window.deleteLecture = deleteLecture;
window.toggleLectureContent = toggleLectureContent;
window.saveCourse = saveCourse;
window.saveChapter = saveChapter;
window.saveLecture = saveLecture;
