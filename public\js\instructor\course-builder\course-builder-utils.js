/**
 * Course Builder Utilities Module
 * Shared utility functions and error handling
 */

// Utility functions
function showSuccess(message) {
    showSaveStatus('saved', message);
}

function showError(message) {
    showSaveStatus('error', message);
}

function showSaveStatus(status, message) {
    const indicator = document.getElementById('save-status-indicator');
    const statusText = document.getElementById('save-status-text');

    if (!indicator || !statusText) return;

    // Remove existing status classes
    indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

    switch (status) {
        case 'saving':
            indicator.className += ' bg-blue-600 text-white';
            statusText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
            break;
        case 'saved':
            indicator.className += ' bg-green-600 text-white';
            statusText.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
            break;
        case 'error':
            indicator.className += ' bg-red-600 text-white';
            statusText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
            break;
    }

    indicator.classList.remove('hidden');

    // Auto-hide success/error messages
    if (status !== 'saving') {
        setTimeout(() => {
            indicator.classList.add('hidden');
        }, 3000);
    }
}
